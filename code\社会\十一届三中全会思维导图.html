<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>十一届三中全会思维导图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            overflow-x: auto;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .title {
            text-align: center;
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 40px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .print-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .mindmap {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            grid-template-rows: auto auto auto auto auto;
            gap: 20px;
            max-width: 1600px;
            margin: 0 auto;
            padding: 30px 15px;
        }

        .central-node {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 20px;
            border-radius: 50px;
            font-size: 1.2em;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 10px 30px rgba(238, 90, 36, 0.3);
            grid-column: 2 / 4;
            grid-row: 3;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100px;
        }

        .branch {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-left: 5px solid;
            transition: all 0.3s ease;
            position: relative;
            font-size: 0.9em;
        }

        .branch:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .branch-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .branch-content {
            font-size: 0.95em;
            line-height: 1.6;
            color: #34495e;
        }

        .branch-content div {
            margin-bottom: 10px;
            padding: 5px 0;
        }

        /* 分支位置和颜色 */
        .branch1 {
            grid-column: 1;
            grid-row: 1;
            border-color: #3498db;
        }

        .branch2 {
            grid-column: 1;
            grid-row: 2;
            border-color: #2ecc71;
        }

        .branch3 {
            grid-column: 1;
            grid-row: 4;
            border-color: #9b59b6;
        }

        .branch4 {
            grid-column: 4;
            grid-row: 1;
            border-color: #f39c12;
        }

        .branch5 {
            grid-column: 4;
            grid-row: 2;
            border-color: #e74c3c;
        }

        .branch6 {
            grid-column: 1;
            grid-row: 5;
            border-color: #16a085;
        }

        .branch7 {
            grid-column: 2;
            grid-row: 5;
            border-color: #8e44ad;
        }

        .branch8 {
            grid-column: 3;
            grid-row: 5;
            border-color: #d35400;
        }

        .branch9 {
            grid-column: 4;
            grid-row: 4;
            border-color: #27ae60;
        }

        .branch10 {
            grid-column: 4;
            grid-row: 5;
            border-color: #2980b9;
        }

        .branch11 {
            grid-column: 1;
            grid-row: 3;
            border-color: #c0392b;
        }

        .branch12 {
            grid-column: 2;
            grid-row: 1;
            border-color: #7f8c8d;
        }

        .branch13 {
            grid-column: 3;
            grid-row: 1;
            border-color: #34495e;
        }

        /* 连接线效果 */
        .branch::before {
            content: '';
            position: absolute;
            width: 30px;
            height: 3px;
            background: linear-gradient(90deg, transparent, #bdc3c7, transparent);
            top: 50%;
            transform: translateY(-50%);
        }

        .branch1::before, .branch2::before, .branch3::before {
            right: -30px;
        }

        .branch4::before, .branch5::before, .branch6::before {
            left: -30px;
        }

        .branch7::before {
            width: 30px;
            height: 3px;
            background: linear-gradient(90deg, transparent, #bdc3c7, transparent);
            top: -30px;
            left: 50%;
            transform: translateX(-50%) rotate(90deg);
        }

        .highlight {
            background: #fff3cd;
            border-radius: 5px;
            padding: 2px 5px;
            margin: 2px 0;
        }

        .year {
            color: #e74c3c;
            font-weight: bold;
        }

        @media (max-width: 1400px) {
            .mindmap {
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                padding: 20px;
            }

            .central-node {
                grid-column: 1 / 3;
                grid-row: 1;
                order: 1;
            }

            .branch1 { grid-column: 1; grid-row: 2; order: 2; }
            .branch2 { grid-column: 2; grid-row: 2; order: 3; }
            .branch3 { grid-column: 1; grid-row: 3; order: 4; }
            .branch4 { grid-column: 2; grid-row: 3; order: 5; }
            .branch5 { grid-column: 1; grid-row: 4; order: 6; }
            .branch6 { grid-column: 2; grid-row: 4; order: 7; }
            .branch7 { grid-column: 1; grid-row: 5; order: 8; }
            .branch8 { grid-column: 2; grid-row: 5; order: 9; }
            .branch9 { grid-column: 1; grid-row: 6; order: 10; }
            .branch10 { grid-column: 2; grid-row: 6; order: 11; }
            .branch11 { grid-column: 1; grid-row: 7; order: 12; }
            .branch12 { grid-column: 2; grid-row: 7; order: 13; }
            .branch13 { grid-column: 1; grid-row: 8; order: 14; }

            .branch::before {
                display: none;
            }
        }

        @media (max-width: 900px) {
            .mindmap {
                grid-template-columns: 1fr;
                gap: 15px;
                padding: 15px;
            }

            .central-node {
                grid-column: 1;
                grid-row: 1;
                order: 1;
            }

            .branch1 { grid-column: 1; grid-row: 2; order: 2; }
            .branch2 { grid-column: 1; grid-row: 3; order: 3; }
            .branch3 { grid-column: 1; grid-row: 4; order: 4; }
            .branch4 { grid-column: 1; grid-row: 5; order: 5; }
            .branch5 { grid-column: 1; grid-row: 6; order: 6; }
            .branch6 { grid-column: 1; grid-row: 7; order: 7; }
            .branch7 { grid-column: 1; grid-row: 8; order: 8; }
            .branch8 { grid-column: 1; grid-row: 9; order: 9; }
            .branch9 { grid-column: 1; grid-row: 10; order: 10; }
            .branch10 { grid-column: 1; grid-row: 11; order: 11; }
            .branch11 { grid-column: 1; grid-row: 12; order: 12; }
            .branch12 { grid-column: 1; grid-row: 13; order: 13; }
            .branch13 { grid-column: 1; grid-row: 14; order: 14; }
        }

        @media (max-width: 768px) {
            .title {
                font-size: 2em;
            }

            .central-node {
                font-size: 1.2em;
                padding: 20px;
            }

            .branch {
                padding: 20px;
            }

            .branch-title {
                font-size: 1.1em;
            }

            .branch-content {
                font-size: 0.9em;
            }
        }

        /* 打印样式 */
        @media print {
            body {
                background: white !important;
                padding: 0 !important;
                margin: 0 !important;
                font-size: 10px !important;
            }

            .container {
                background: white !important;
                box-shadow: none !important;
                border-radius: 0 !important;
                padding: 15px !important;
                margin: 0 !important;
                max-width: none !important;
                width: 100% !important;
                height: 100vh !important;
            }

            .print-button {
                display: none !important;
            }

            .title {
                font-size: 1.8em !important;
                margin-bottom: 20px !important;
                text-shadow: none !important;
                color: #000 !important;
            }

            .mindmap {
                grid-template-columns: repeat(4, 1fr) !important;
                grid-template-rows: repeat(5, auto) !important;
                gap: 8px !important;
                max-width: none !important;
                padding: 0 !important;
                margin: 0 !important;
                transform: scale(0.75) !important;
                transform-origin: top left !important;
            }

            .central-node {
                background: #f0f0f0 !important;
                color: #000 !important;
                padding: 10px !important;
                font-size: 0.9em !important;
                box-shadow: none !important;
                border: 2px solid #333 !important;
                min-height: 60px !important;
            }

            .branch {
                background: white !important;
                border: 1px solid #333 !important;
                border-left: 3px solid #333 !important;
                padding: 8px !important;
                box-shadow: none !important;
                font-size: 0.7em !important;
                page-break-inside: avoid !important;
            }

            .branch-title {
                font-size: 0.8em !important;
                margin-bottom: 5px !important;
                color: #000 !important;
                font-weight: bold !important;
            }

            .branch-content {
                font-size: 0.7em !important;
                line-height: 1.3 !important;
                color: #000 !important;
            }

            .branch-content div {
                margin-bottom: 3px !important;
                padding: 2px 0 !important;
            }

            .highlight {
                background: #f5f5f5 !important;
                border: 1px solid #ccc !important;
                color: #000 !important;
            }

            .year {
                color: #000 !important;
                font-weight: bold !important;
            }

            .branch::before {
                display: none !important;
            }

            /* 确保所有内容在一页内 */
            @page {
                size: A4;
                margin: 10mm;
            }
        }
    </style>
</head>
<body>
    <button class="print-button" onclick="window.print()">🖨️ 打印</button>

    <div class="container">
        <h1 class="title">十一届三中全会思维导图</h1>
        
        <div class="mindmap">
            <!-- 分支1：召开背景 -->
            <div class="branch branch1">
                <div class="branch-title">1. 召开的背景</div>
                <div class="branch-content">
                    <div class="highlight">① 人们要求纠正文革的错误</div>
                    <div class="highlight">② "两个凡是"方针的推行引起普遍不满</div>
                    <div class="highlight">③ <span class="year">1978年</span>，思想解放开了一场真理标准的大讨论</div>
                </div>
            </div>

            <!-- 分支2：主要内容 -->
            <div class="branch branch2">
                <div class="branch-title">2. 主要内容</div>
                <div class="branch-content">
                    <div><strong>思想上：</strong>冲破了长期"左"的错误的严重束缚，确定了解放思想、开动脑筋、实事求是、团结一致向前看的指导方针</div>
                    <div><strong>政治上：</strong>果断结束"以阶级斗争为纲"，重新确立马克思主义的思想路线、政治路线、组织路线</div>
                    <div><strong>组织上：</strong>形成了以邓小平为核心的党的第二代中央领导集体</div>
                </div>
            </div>

            <!-- 中心节点 -->
            <div class="central-node">
                十一届三中全会<br>
                <span class="year">(1978年12月，北京)</span>
            </div>

            <!-- 分支4：拔乱反正成果 -->
            <div class="branch branch4">
                <div class="branch-title">4. 拔乱反正有哪些成果</div>
                <div class="branch-content">
                    <div><strong>政治上：</strong>平反冤假错案</div>
                    <div><strong>思想上：</strong>召开中共十一届六中全会</div>
                    <div class="highlight"><strong>教育上：</strong>恢复高考制度(<span class="year">1977年</span>)</div>
                </div>
            </div>

            <!-- 分支5：改革开放 -->
            <div class="branch branch5">
                <div class="branch-title">5. 改革开放</div>
                <div class="branch-content">
                    <div class="highlight"><strong>改革方向：</strong>从计划经济逐渐向社会主义市场经济</div>
                    <div class="highlight"><strong>改革过程：</strong>从农村到城市</div>
                    <div class="highlight"><strong>改革目标：</strong>建立社会主义市场经济体制</div>
                    <div class="highlight"><strong>改革实质：</strong>社会主义制度的自我完善和发展</div>
                </div>
            </div>

            <!-- 分支6：农村经济体制改革 -->
            <div class="branch branch6">
                <div class="branch-title">6. 农村经济体制改革</div>
                <div class="branch-content">
                    <div><strong>开始时间：</strong><span class="year">1978年</span>，安村</div>
                    <div class="highlight"><strong>制度：</strong>家庭联产承包责任制</div>
                    <div class="highlight"><strong>典型：</strong>安徽凤阳小岗村</div>
                    <div><strong>主要内容：</strong>分田包产到户，自负盈亏</div>
                    <div><strong>作用：</strong>激发了农民的劳动热情，带来了农业生产力的大解放，使农民生产和收入大大提高</div>
                </div>
            </div>

            <!-- 分支7：经济体制改革核心 -->
            <div class="branch branch7">
                <div class="branch-title">7. 经济体制改革的核心</div>
                <div class="branch-content">
                    <div class="highlight"><strong>核心：</strong>增强企业活力</div>
                </div>
            </div>

            <!-- 分支8：我国改革开放的开始 -->
            <div class="branch branch8">
                <div class="branch-title">8. 我国改革开放的开始</div>
                <div class="branch-content">
                    <div class="highlight"><strong>时间：</strong><span class="year">1978年</span>，农村</div>
                </div>
            </div>

            <!-- 分支9：对外开放 -->
            <div class="branch branch9">
                <div class="branch-title">9. 对外开放</div>
                <div class="branch-content">
                    <div class="highlight"><strong>发展乡镇企业：</strong>为发展乡镇企业和实现现代化开辟了一条新路</div>
                </div>
            </div>

            <!-- 分支10：农村经济体制改革成果 -->
            <div class="branch branch10">
                <div class="branch-title">10. 我国农村经济体制改革</div>
                <div class="branch-content">
                    <div class="highlight"><strong>制度：</strong>家庭联产承包责任制，在农村实行什么制度？典型例子是什么？</div>
                    <div class="highlight"><strong>典型：</strong>安徽凤阳小岗村</div>
                    <div><strong>主要内容：</strong>分田包产到户，自负盈亏</div>
                    <div><strong>作用：</strong>激发了农民的劳动热情，带来了农业生产力的大解放，使农民生产和收入大大提高</div>
                </div>
            </div>

            <!-- 分支11：为发展乡镇企业 -->
            <div class="branch branch11">
                <div class="branch-title">11. 为发展乡镇企业和实现现代化开辟了一条新路的是什么？</div>
                <div class="branch-content">
                    <div class="highlight"><strong>答案：</strong>发展乡镇企业</div>
                </div>
            </div>

            <!-- 分支12：新时期农村经济体制改革有哪些？ -->
            <div class="branch branch12">
                <div class="branch-title">12. 新时期农村经济体制改革有哪些？其中大大提高了农民生产积极性的有哪些？</div>
                <div class="branch-content">
                    <div><strong>土地改革：</strong><span class="year">1950-1952年</span>的土地改革（大大提高）</div>
                    <div><strong>农业合作化：</strong><span class="year">1953-1956年</span>的农业合作化运动（大大提高）</div>
                    <div><strong>人民公社化：</strong><span class="year">1958年</span>开始的人民公社化运动（严重挫折）</div>
                    <div><strong>家庭联产承包：</strong><span class="year">1978年</span>开始的家庭联产承包责任制（大大提高）</div>
                </div>
            </div>

            <!-- 分支13：城市经济体制改革的中心环节是什么？ -->
            <div class="branch branch13">
                <div class="branch-title">13. 城市经济体制改革的中心环节是什么？</div>
                <div class="branch-content">
                    <div class="highlight"><strong>答案：</strong>增强企业活力</div>
                </div>
            </div>

            <!-- 分支3：历史意义 -->
            <div class="branch branch3">
                <div class="branch-title">3. 历史意义</div>
                <div class="branch-content">
                    <div class="highlight">① 是新中国成立以来党的历史上具有深远意义的伟大转折</div>
                    <div class="highlight">② 开启了改革开放和社会主义现代化建设新时期</div>
                    <div class="highlight">③ 中华人民共和国成立以来最大的发展机遇(<span class="year">1980年</span>)</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const branches = document.querySelectorAll('.branch');

            branches.forEach(branch => {
                branch.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                branch.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // 中心节点点击效果
            const centralNode = document.querySelector('.central-node');
            centralNode.addEventListener('click', function() {
                branches.forEach((branch, index) => {
                    setTimeout(() => {
                        branch.style.animation = 'pulse 0.6s ease-in-out';
                    }, index * 200);
                });
            });

            // 打印前优化布局
            window.addEventListener('beforeprint', function() {
                document.body.style.fontSize = '10px';
            });

            window.addEventListener('afterprint', function() {
                document.body.style.fontSize = '';
            });
        });

        // 添加脉冲动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
